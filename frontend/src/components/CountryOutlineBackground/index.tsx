import React, { useEffect, useRef } from 'react';

const CountryOutlineBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationIdRef = useRef<number | null>(null);
  const mountedRef = useRef<boolean>(false);
  const particlesRef = useRef<any[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    if (!canvasRef.current || mountedRef.current) return;

    mountedRef.current = true;
    console.log('CountryOutlineBackground: 开始初始化粒子系统');

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布尺寸
    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width || window.innerWidth;
      canvas.height = rect.height || window.innerHeight;
    };

    resizeCanvas();

    // 粒子类
    class Particle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      color: string;
      life: number;
      maxLife: number;
      alpha: number;

      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.vx = (Math.random() - 0.5) * 1.2;
        this.vy = (Math.random() - 0.5) * 1.2;
        this.size = Math.random() * 2.5 + 1;

        // 使用地理计算主题色彩
        const colors = ['#00ff7f', '#4FC3F7', '#BA68C8', '#81C784'];
        this.color = colors[Math.floor(Math.random() * colors.length)];

        this.life = Math.random() * 100 + 50;
        this.maxLife = this.life;
        this.alpha = 1;
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life--;

        // 边界反弹
        if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
        if (this.y < 0 || this.y > canvas.height) this.vy *= -1;

        // 重生机制
        if (this.life <= 0) {
          this.x = Math.random() * canvas.width;
          this.y = Math.random() * canvas.height;
          this.life = this.maxLife;
        }

        this.alpha = this.life / this.maxLife;
      }

      draw() {
        ctx.save();
        ctx.globalAlpha = this.alpha;

        // 绘制粒子
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();

        // 添加发光效果
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 15;
        ctx.fill();

        ctx.restore();
      }
    }

    // 创建粒子数组
    const particleCount = 40; // 控制在30-50个粒子
    const particles: Particle[] = [];

    // 初始化粒子
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle());
    }

    particlesRef.current = particles;

    // 绘制连接线的函数
    const drawConnections = () => {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 120) {
            const alpha = (1 - distance / 120) * 0.3;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.strokeStyle = `rgba(0, 255, 127, ${alpha})`;
            ctx.lineWidth = 0.8;
            ctx.stroke();
          }
        }
      }
    };

    // 鼠标交互效果
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouseRef.current.x = e.clientX - rect.left;
      mouseRef.current.y = e.clientY - rect.top;
    };

    // 动画循环
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);

      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 更新和绘制粒子
      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });

      // 绘制连接线
      drawConnections();
    };

    // 窗口大小变化处理
    const handleResize = () => {
      resizeCanvas();
    };

    // 添加事件监听器
    canvas.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', handleResize);

    // 开始动画
    animate();

    console.log('粒子系统初始化完成，包含', particleCount, '个粒子');

    // 清理函数
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      canvas.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      console.log('CountryOutlineBackground: 所有资源清理完成');
    };







    // 设置相机位置
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // 更新粒子独立运动的函数
    function updateParticleMotion(time: number) {
      if (!particleSystemRef.current) return;

      const particles = particleSystemRef.current as any;
      const particleData = particles.particleData;
      const geometry = particles.particleGeometry;

      if (!particleData || !geometry) return;

      const positions = geometry.attributes.position.array;

      // 更新每个粒子的位置
      for (let i = 0; i < particleData.length; i++) {
        const data = particleData[i];

        // 计算当前时间的轨道位置
        const orbitTime = time * data.orbitSpeed + data.orbitOffset;
        const heightTime = time * data.heightSpeed + data.phaseOffset;

        // 计算动态半径（柔和的高度变化）
        const heightVariation = Math.sin(heightTime) * data.heightAmplitude;
        const secondaryHeight = Math.cos(heightTime * 1.3) * data.heightAmplitude * 0.3; // 添加次级高度变化
        const dynamicRadius = data.baseRadius + heightVariation + secondaryHeight;

        // 计算动态角度（更自然的轨道运动）
        const primaryOrbit = Math.sin(orbitTime) * 0.12; // 主要轨道摆动
        const secondaryOrbit = Math.sin(orbitTime * 1.7) * 0.05; // 次级轨道摆动
        const dynamicPhi = data.basePhi + primaryOrbit + secondaryOrbit;

        const primaryTilt = Math.cos(orbitTime * 0.8) * 0.08; // 主要倾斜
        const secondaryTilt = Math.cos(orbitTime * 2.1) * 0.03; // 次级倾斜
        const dynamicTheta = data.baseTheta + primaryTilt + secondaryTilt;

        // 球面坐标转换为笛卡尔坐标
        const x = dynamicRadius * Math.sin(dynamicTheta) * Math.cos(dynamicPhi);
        const y = dynamicRadius * Math.cos(dynamicTheta);
        const z = dynamicRadius * Math.sin(dynamicTheta) * Math.sin(dynamicPhi);

        // 更新位置
        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;
      }

      // 标记几何体需要更新
      geometry.attributes.position.needsUpdate = true;
    }

    // 主动画循环 - 控制所有视觉效果的更新
    function animate() {
      animationIdRef.current = requestAnimationFrame(animate);
      const time = Date.now() * 0.001; // 获取当前时间，用于所有动画计算

      // 缓慢旋转地球，模拟地球自转
      earthGroup.rotation.y += 0.0015; // 进一步优化旋转速度
      earthGroup.rotation.x += 0.0003; // 轻微的倾斜旋转

      // 更新粒子的独立运动
      updateParticleMotion(time);

      // 渲染场景
      renderer.render(scene, camera);
    }

    // 添加调试信息
    console.log('场景对象数量:', scene.children.length);
    console.log('地球组对象数量:', earthGroup.children.length);

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.width || window.innerWidth;
      const newHeight = containerRect.height || window.innerHeight;

      camera.aspect = newWidth / newHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(newWidth, newHeight);
      console.log('窗口大小调整:', newWidth, 'x', newHeight);
    };

    window.addEventListener('resize', handleResize);

    // 监听主题变化
    let observer: MutationObserver | null = null;

    const initThemeObserver = () => {
      observer = new MutationObserver(() => {
        updateColors();
      });
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    };

    // 开始动画
    animate();

    // 初始化主题监听器
    initThemeObserver();

    // 尝试加载真实地理数据，失败时使用备选球体
    loadWorldData().then(() => {
      console.log('真实地理数据加载并渲染完成');
      console.log('当前地球组包含对象数量:', earthGroup.children.length);

      // 创建粒子系统效果
      createDataParticles();
      console.log('精致粒子系统效果创建完成');
    }).catch((error) => {
      console.log('地理数据加载失败，使用备选球体:', error.message);
      createFallbackSphere();
      console.log('备选球体创建完成，地球组包含对象数量:', earthGroup.children.length);

      // 即使使用备选球体，也创建粒子系统效果
      createDataParticles();
      console.log('备选模式下精致粒子系统效果创建完成');
    });

    // 组件清理函数 - 释放所有THREE.js资源和事件监听器
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      // 清理粒子系统资源
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.PointsMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = particleSystemRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        particleSystemRef.current = null;
      }

      // 清理渲染器和DOM元素
      if (rendererRef.current && containerRef.current && containerRef.current.contains(rendererRef.current.domElement)) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
        rendererRef.current = null;
      }

      // 清理场景
      if (sceneRef.current) {
        sceneRef.current.clear();
        sceneRef.current = null;
      }



      console.log('CountryOutlineBackground: 所有资源清理完成');
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2,
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default CountryOutlineBackground;
