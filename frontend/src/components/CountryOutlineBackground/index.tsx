import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';

const CountryOutlineBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const mountedRef = useRef<boolean>(false);

  // 粒子系统相关引用
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const connectionLinesRef = useRef<THREE.LineSegments | null>(null);

  useEffect(() => {
    if (!containerRef.current || mountedRef.current) return;

    mountedRef.current = true;
    console.log('CountryOutlineBackground: 开始初始化');

    // 检查THREE.js是否可用
    if (typeof THREE === 'undefined') {
      console.error('THREE.js library is not loaded');
      return;
    }

    console.log('THREE.js 版本:', THREE.REVISION);

    // 获取容器尺寸
    const containerRect = containerRef.current.getBoundingClientRect();
    const width = containerRect.width || window.innerWidth;
    const height = containerRect.height || window.innerHeight;

    // 场景设置
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      premultipliedAlpha: false
    });

    // 配置渲染器
    renderer.setSize(width, height);
    renderer.setClearColor(0x000000, 0); // 完全透明背景
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能

    // 设置canvas样式
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.pointerEvents = 'none';
    renderer.domElement.style.zIndex = '1';

    containerRef.current.appendChild(renderer.domElement);
    console.log('渲染器初始化完成, 尺寸:', width, 'x', height);

    // 保存引用
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // 地球半径
    const EARTH_RADIUS = 2;

    // 创建地球组
    const earthGroup = new THREE.Group();
    scene.add(earthGroup);

    // 创建粒子系统组
    const particleGroup = new THREE.Group();
    scene.add(particleGroup);

    // 获取CSS变量颜色
    const getThemeColor = (variable: string) => {
      try {
        const color = getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
        console.log(`获取颜色 ${variable}:`, color);

        // 如果颜色为空或无效，使用默认颜色
        if (!color || color === '') {
          console.warn(`颜色变量 ${variable} 为空，使用默认颜色`);
          return variable === '--color-primary' ? '#007aff' : '#5856d6';
        }

        return color;
      } catch (error) {
        console.error(`获取颜色变量 ${variable} 失败:`, error);
        return variable === '--color-primary' ? '#007aff' : '#5856d6';
      }
    };

    // 监听主题变化
    const updateColors = () => {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');

      // 更新线条材质颜色
      earthGroup.children.forEach(child => {
        if (child instanceof THREE.Line) {
          (child.material as THREE.LineBasicMaterial).color.set(primaryColor);
        }
      });

      // 更新粒子颜色以响应主题切换
      updateParticleColors();
    };

    // 将经纬度转换为3D坐标
    function latLngToVector3(lat: number, lng: number, radius = EARTH_RADIUS) {
      const phi = (90 - lat) * (Math.PI / 180);
      const theta = (lng + 180) * (Math.PI / 180);

      const x = -(radius * Math.sin(phi) * Math.cos(theta));
      const z = (radius * Math.sin(phi) * Math.sin(theta));
      const y = (radius * Math.cos(phi));

      return new THREE.Vector3(x, y, z);
    }

    // 从坐标数组创建线条几何体
    function createLineFromCoordinates(coordinates: number[][]) {
      const points: THREE.Vector3[] = [];
      coordinates.forEach(coord => {
        if (coord.length >= 2) {
          const [lng, lat] = coord;
          points.push(latLngToVector3(lat, lng));
        }
      });
      return new THREE.BufferGeometry().setFromPoints(points);
    }

    // 处理GeoJSON数据并创建线框
    function processGeoJSON(geoData: any) {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');
      
      const lineMaterial = new THREE.LineBasicMaterial({
        color: new THREE.Color(primaryColor),
        transparent: true,
        opacity: 0.8,
      });

      geoData.features.forEach((feature: any) => {
        if (feature.geometry) {
          const { type, coordinates } = feature.geometry;

          if (type === 'Polygon') {
            coordinates.forEach((ring: number[][]) => {
              const geometry = createLineFromCoordinates(ring);
              const line = new THREE.Line(geometry, lineMaterial);
              earthGroup.add(line);
            });
          } else if (type === 'MultiPolygon') {
            coordinates.forEach((polygon: number[][][]) => {
              polygon.forEach((ring: number[][]) => {
                const geometry = createLineFromCoordinates(ring);
                const line = new THREE.Line(geometry, lineMaterial);
                earthGroup.add(line);
              });
            });
          }
        }
      });
    }



    // 加载世界地理数据
    async function loadWorldData() {
      console.log('正在加载世界地理数据...');
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoData = await response.json();
      console.log('地理数据加载成功，包含', geoData.features.length, '个国家/地区');

      // 添加真实的地理数据
      processGeoJSON(geoData);
      console.log('真实地球线框渲染完成');
    }

    // 备选方案：创建简单的球体线框
    function createFallbackSphere() {
      console.log('使用备选球体线框');
      const sphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 16);
      const sphereMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color(getThemeColor('--color-primary')),
        wireframe: true,
        transparent: true,
        opacity: 0.8
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      earthGroup.add(sphere);
      console.log('备选球体添加完成');
    }

    // 获取主题响应式粒子颜色的函数
    function getParticleColors() {
      const isDarkTheme = document.documentElement.classList.contains('dark');

      if (isDarkTheme) {
        // 深色主题：使用较亮的颜色确保在深色背景下可见
        return {
          spatial: new THREE.Color('#4FC3F7'),    // 亮蓝色 - 空间分析
          urban: new THREE.Color('#BA68C8'),      // 亮紫色 - 城市计算
          economic: new THREE.Color('#81C784')    // 亮绿色 - 经济建模
        };
      } else {
        // 浅色主题：使用更深、对比度更高的颜色确保在浅色背景下清晰可见
        return {
          spatial: new THREE.Color('#0D47A1'),    // 更深的蓝色 - 空间分析
          urban: new THREE.Color('#4A148C'),      // 更深的紫色 - 城市计算
          economic: new THREE.Color('#1B5E20')    // 更深的绿色 - 经济建模
        };
      }
    }

    // 更新粒子颜色以响应主题切换
    function updateParticleColors() {
      if (!particleSystemRef.current) return;

      const particles = particleSystemRef.current as any;
      const particleData = particles.particleData;
      const geometry = particles.particleGeometry;

      if (!particleData || !geometry) return;

      const colors = geometry.attributes.color.array;
      const particleColors = getParticleColors();

      console.log('更新粒子颜色，当前主题:', document.documentElement.classList.contains('dark') ? 'dark' : 'light');

      // 更新每个粒子的颜色
      for (let i = 0; i < particleData.length; i++) {
        const data = particleData[i];
        let color: THREE.Color;

        if (data.colorType === 'spatial') {
          color = particleColors.spatial;
        } else if (data.colorType === 'urban') {
          color = particleColors.urban;
        } else {
          color = particleColors.economic;
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;
      }

      // 标记颜色属性需要更新
      geometry.attributes.color.needsUpdate = true;
    }

    // 创建自由围绕地球活动的粒子系统 - 参考提供的HTML代码实现
    function createFreeFloatingParticles() {
      const particleCount = 35; // 30-50个粒子，调整为35个以获得更好的视觉效果

      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);

      // 获取主题响应式颜色
      const particleColors = getParticleColors();

      // 为每个粒子创建独立的运动参数
      const particleData: Array<{
        x: number;
        y: number;
        z: number;
        vx: number;
        vy: number;
        vz: number;
        size: number;
        life: number;
        maxLife: number;
        colorType: 'spatial' | 'urban' | 'economic';
        orbitRadius: number;
        orbitSpeed: number;
        orbitAngle: number;
        heightOffset: number;
        heightSpeed: number;
      }> = [];

      // 创建粒子，让它们自由围绕地球活动
      for (let i = 0; i < particleCount; i++) {
        // 随机分布在地球周围的球形空间内
        const orbitRadius = EARTH_RADIUS + 0.8 + Math.random() * 1.2; // 距离地球0.8-2.0单位，更紧密围绕地球
        const phi = Math.random() * Math.PI * 2; // 经度
        const theta = Math.random() * Math.PI; // 纬度

        // 初始位置
        const x = orbitRadius * Math.sin(theta) * Math.cos(phi);
        const y = orbitRadius * Math.cos(theta);
        const z = orbitRadius * Math.sin(theta) * Math.sin(phi);

        // 随机速度，让粒子自由移动，增加速度让运动更明显
        const speed = 0.008 + Math.random() * 0.012; // 增加速度，让运动更明显
        const vx = (Math.random() - 0.5) * speed;
        const vy = (Math.random() - 0.5) * speed;
        const vz = (Math.random() - 0.5) * speed;

        // 确定粒子的颜色类型
        const colorChoice = Math.random();
        let colorType: 'spatial' | 'urban' | 'economic';
        if (colorChoice < 0.4) {
          colorType = 'spatial';
        } else if (colorChoice < 0.7) {
          colorType = 'urban';
        } else {
          colorType = 'economic';
        }

        const particleInfo = {
          x: x,
          y: y,
          z: z,
          vx: vx,
          vy: vy,
          vz: vz,
          size: Math.random() * 0.02 + 0.01, // 粒子大小
          life: Math.random() * 300 + 200, // 更长的生命周期，让粒子存在更久
          maxLife: 0,
          colorType: colorType,
          orbitRadius: orbitRadius,
          orbitSpeed: 0.002 + Math.random() * 0.003, // 增加轨道速度让运动更明显
          orbitAngle: Math.random() * Math.PI * 2, // 轨道角度
          heightOffset: Math.random() * 0.3, // 增加高度偏移
          heightSpeed: 0.003 + Math.random() * 0.004 // 增加高度变化速度
        };
        particleInfo.maxLife = particleInfo.life;
        particleData.push(particleInfo);

        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;

        // 根据颜色类型分配颜色
        let color: THREE.Color;
        if (particleInfo.colorType === 'spatial') {
          color = particleColors.spatial;
        } else if (particleInfo.colorType === 'urban') {
          color = particleColors.urban;
        } else {
          color = particleColors.economic;
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;
      }

      const geometry = new THREE.BufferGeometry();
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

      // 创建发光的圆形纹理
      const canvas = document.createElement('canvas');
      canvas.width = 64;
      canvas.height = 64;
      const context = canvas.getContext('2d')!;

      // 创建径向渐变，模拟发光效果
      const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
      gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
      gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.8)');
      gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.3)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      context.fillStyle = gradient;
      context.fillRect(0, 0, 64, 64);

      const texture = new THREE.CanvasTexture(canvas);

      // 创建粒子材质
      const material = new THREE.PointsMaterial({
        size: 0.18, // 稍微增大粒子大小让其更明显
        map: texture,
        vertexColors: true,
        transparent: true,
        opacity: 0.9, // 增加不透明度
        blending: THREE.AdditiveBlending, // 使用加法混合创建发光效果
        depthWrite: false,
        sizeAttenuation: true
      });

      const particles = new THREE.Points(geometry, material);

      // 保存粒子数据和几何体引用
      (particles as any).particleData = particleData;
      (particles as any).particleGeometry = geometry;

      // 将粒子添加到独立的粒子组，不跟随地球旋转
      particleGroup.add(particles);
      particleSystemRef.current = particles;

      // 创建粒子连接线
      createParticleConnections(particleCount);

      console.log('自由围绕地球的粒子系统创建完成，包含', particleCount, '个动态粒子');
    }

    // 创建粒子之间的连接线
    function createParticleConnections(particleCount: number) {
      const maxConnections = particleCount * 2; // 最大连接数
      const connectionGeometry = new THREE.BufferGeometry();
      const connectionPositions = new Float32Array(maxConnections * 6); // 每条线需要6个坐标值（起点和终点）
      const connectionColors = new Float32Array(maxConnections * 6); // 每条线需要6个颜色值

      connectionGeometry.setAttribute('position', new THREE.BufferAttribute(connectionPositions, 3));
      connectionGeometry.setAttribute('color', new THREE.BufferAttribute(connectionColors, 3));

      const connectionMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        transparent: true,
        opacity: 0.5, // 进一步增加透明度让连接线更明显
        blending: THREE.AdditiveBlending
      });

      const connectionLines = new THREE.LineSegments(connectionGeometry, connectionMaterial);
      particleGroup.add(connectionLines);
      connectionLinesRef.current = connectionLines;

      console.log('粒子连接线系统创建完成');
    }







    // 设置相机位置
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // 更新自由粒子运动的函数
    function updateFreeFloatingParticles(time: number) {
      if (!particleSystemRef.current) return;

      const particles = particleSystemRef.current as any;
      const particleData = particles.particleData;
      const geometry = particles.particleGeometry;

      if (!particleData || !geometry) return;

      // 每5秒输出一次调试信息
      if (Math.floor(time) % 5 === 0 && Math.floor(time * 10) % 10 === 0) {
        console.log('粒子运动更新中，时间:', time.toFixed(2), '粒子数量:', particleData.length);
      }

      const positions = geometry.attributes.position.array;

      // 更新每个粒子的位置
      for (let i = 0; i < particleData.length; i++) {
        const data = particleData[i];

        // 更新粒子位置
        data.x += data.vx;
        data.y += data.vy;
        data.z += data.vz;

        // 计算距离地球中心的距离
        const distanceFromCenter = Math.sqrt(data.x * data.x + data.y * data.y + data.z * data.z);

        // 如果粒子距离地球太远，施加向心力
        if (distanceFromCenter > EARTH_RADIUS + 2.5) {
          const pullStrength = 0.0002; // 增加向心力让运动更明显
          data.vx -= data.x * pullStrength;
          data.vy -= data.y * pullStrength;
          data.vz -= data.z * pullStrength;
        }

        // 如果粒子太靠近地球，施加排斥力
        if (distanceFromCenter < EARTH_RADIUS + 0.5) {
          const pushStrength = 0.0003; // 增加排斥力
          data.vx += data.x * pushStrength;
          data.vy += data.y * pushStrength;
          data.vz += data.z * pushStrength;
        }

        // 添加轻微的轨道运动
        data.orbitAngle += data.orbitSpeed;
        const orbitInfluence = 0.004; // 增加轨道影响让运动更明显
        data.vx += Math.cos(data.orbitAngle) * orbitInfluence;
        data.vz += Math.sin(data.orbitAngle) * orbitInfluence;

        // 添加高度变化
        const heightVariation = Math.sin(time * data.heightSpeed) * data.heightOffset;
        data.vy += heightVariation * 0.0002; // 增加高度变化影响

        // 限制速度，避免粒子运动过快
        const maxSpeed = 0.025; // 增加最大速度
        const currentSpeed = Math.sqrt(data.vx * data.vx + data.vy * data.vy + data.vz * data.vz);
        if (currentSpeed > maxSpeed) {
          const scale = maxSpeed / currentSpeed;
          data.vx *= scale;
          data.vy *= scale;
          data.vz *= scale;
        }

        // 更新生命周期
        data.life--;
        if (data.life <= 0) {
          // 重新生成粒子
          const orbitRadius = EARTH_RADIUS + 0.5 + Math.random() * 1.5;
          const phi = Math.random() * Math.PI * 2;
          const theta = Math.random() * Math.PI;

          data.x = orbitRadius * Math.sin(theta) * Math.cos(phi);
          data.y = orbitRadius * Math.cos(theta);
          data.z = orbitRadius * Math.sin(theta) * Math.sin(phi);

          const speed = 0.008 + Math.random() * 0.012; // 与初始速度保持一致
          data.vx = (Math.random() - 0.5) * speed;
          data.vy = (Math.random() - 0.5) * speed;
          data.vz = (Math.random() - 0.5) * speed;

          data.life = data.maxLife;
        }

        // 更新位置数组
        positions[i * 3] = data.x;
        positions[i * 3 + 1] = data.y;
        positions[i * 3 + 2] = data.z;
      }

      // 标记几何体需要更新
      geometry.attributes.position.needsUpdate = true;

      // 更新粒子连接线
      updateParticleConnections(particleData);
    }

    // 更新粒子之间的连接线
    function updateParticleConnections(particleData: any[]) {
      if (!connectionLinesRef.current) return;

      const connectionGeometry = connectionLinesRef.current.geometry;
      const connectionPositions = connectionGeometry.attributes.position.array as Float32Array;
      const connectionColors = connectionGeometry.attributes.color.array as Float32Array;

      let connectionIndex = 0;
      const maxDistance = 1.8; // 增加最大连接距离，让连接更多
      const particleColors = getParticleColors();

      // 清空连接线
      connectionPositions.fill(0);
      connectionColors.fill(0);

      // 计算粒子之间的连接
      for (let i = 0; i < particleData.length && connectionIndex < connectionPositions.length / 6; i++) {
        for (let j = i + 1; j < particleData.length && connectionIndex < connectionPositions.length / 6; j++) {
          const particle1 = particleData[i];
          const particle2 = particleData[j];

          // 计算距离
          const dx = particle1.x - particle2.x;
          const dy = particle1.y - particle2.y;
          const dz = particle1.z - particle2.z;
          const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

          // 如果距离在连接范围内，创建连接线
          if (distance < maxDistance) {
            // 根据主题调整透明度
            const isDarkTheme = document.documentElement.classList.contains('dark');
            const baseAlpha = isDarkTheme ? 0.6 : 0.8; // 浅色主题下连接线更明显
            const alpha = (1 - distance / maxDistance) * baseAlpha;

            // 设置连接线的起点和终点
            connectionPositions[connectionIndex * 6] = particle1.x;
            connectionPositions[connectionIndex * 6 + 1] = particle1.y;
            connectionPositions[connectionIndex * 6 + 2] = particle1.z;
            connectionPositions[connectionIndex * 6 + 3] = particle2.x;
            connectionPositions[connectionIndex * 6 + 4] = particle2.y;
            connectionPositions[connectionIndex * 6 + 5] = particle2.z;

            // 设置连接线的颜色（使用混合颜色）
            const color1 = particle1.colorType === 'spatial' ? particleColors.spatial :
                          particle1.colorType === 'urban' ? particleColors.urban : particleColors.economic;
            const color2 = particle2.colorType === 'spatial' ? particleColors.spatial :
                          particle2.colorType === 'urban' ? particleColors.urban : particleColors.economic;

            // 起点颜色
            connectionColors[connectionIndex * 6] = color1.r * alpha;
            connectionColors[connectionIndex * 6 + 1] = color1.g * alpha;
            connectionColors[connectionIndex * 6 + 2] = color1.b * alpha;

            // 终点颜色
            connectionColors[connectionIndex * 6 + 3] = color2.r * alpha;
            connectionColors[connectionIndex * 6 + 4] = color2.g * alpha;
            connectionColors[connectionIndex * 6 + 5] = color2.b * alpha;

            connectionIndex++;
          }
        }
      }

      // 标记几何体需要更新
      connectionGeometry.attributes.position.needsUpdate = true;
      connectionGeometry.attributes.color.needsUpdate = true;
    }

    // 主动画循环 - 控制所有视觉效果的更新
    function animate() {
      animationIdRef.current = requestAnimationFrame(animate);
      const time = Date.now() * 0.001; // 获取当前时间，用于所有动画计算

      // 缓慢旋转地球，模拟地球自转
      earthGroup.rotation.y += 0.0015; // 进一步优化旋转速度
      earthGroup.rotation.x += 0.0003; // 轻微的倾斜旋转

      // 更新自由粒子的运动
      updateFreeFloatingParticles(time);

      // 渲染场景
      renderer.render(scene, camera);
    }

    // 添加调试信息
    console.log('场景对象数量:', scene.children.length);
    console.log('地球组对象数量:', earthGroup.children.length);

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.width || window.innerWidth;
      const newHeight = containerRect.height || window.innerHeight;

      camera.aspect = newWidth / newHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(newWidth, newHeight);
      console.log('窗口大小调整:', newWidth, 'x', newHeight);
    };

    window.addEventListener('resize', handleResize);

    // 监听主题变化
    let observer: MutationObserver | null = null;

    const initThemeObserver = () => {
      observer = new MutationObserver(() => {
        updateColors();
        updateParticleColors(); // 添加粒子颜色更新
      });
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme', 'class'] // 监听class变化
      });
    };

    // 开始动画
    animate();

    // 初始化主题监听器
    initThemeObserver();

    // 尝试加载真实地理数据，失败时使用备选球体
    loadWorldData().then(() => {
      console.log('真实地理数据加载并渲染完成');
      console.log('当前地球组包含对象数量:', earthGroup.children.length);

      // 创建自由围绕地球的粒子系统效果
      createFreeFloatingParticles();
      console.log('自由围绕地球的粒子系统效果创建完成');
    }).catch((error) => {
      console.log('地理数据加载失败，使用备选球体:', error.message);
      createFallbackSphere();
      console.log('备选球体创建完成，地球组包含对象数量:', earthGroup.children.length);

      // 即使使用备选球体，也创建自由围绕地球的粒子系统效果
      createFreeFloatingParticles();
      console.log('备选模式下自由围绕地球的粒子系统效果创建完成');
    });

    // 组件清理函数 - 释放所有THREE.js资源和事件监听器
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      // 清理粒子系统资源
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.PointsMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = particleSystemRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        particleSystemRef.current = null;
      }

      // 清理连接线资源
      if (connectionLinesRef.current) {
        const material = connectionLinesRef.current.material as THREE.LineBasicMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = connectionLinesRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        connectionLinesRef.current = null;
      }

      // 清理渲染器和DOM元素
      if (rendererRef.current && containerRef.current && containerRef.current.contains(rendererRef.current.domElement)) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
        rendererRef.current = null;
      }

      // 清理场景
      if (sceneRef.current) {
        sceneRef.current.clear();
        sceneRef.current = null;
      }



      console.log('CountryOutlineBackground: 所有资源清理完成');
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2, // 提高z-index，但仍低于文字内容(z-index: 10)
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default CountryOutlineBackground;
