import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';

const CountryOutlineBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const mountedRef = useRef<boolean>(false);

  // 粒子系统相关引用
  const particleSystemRef = useRef<THREE.Points | null>(null);

  useEffect(() => {
    if (!containerRef.current || mountedRef.current) return;

    mountedRef.current = true;
    console.log('CountryOutlineBackground: 开始初始化');

    // 检查THREE.js是否可用
    if (typeof THREE === 'undefined') {
      console.error('THREE.js library is not loaded');
      return;
    }

    console.log('THREE.js 版本:', THREE.REVISION);

    // 获取容器尺寸
    const containerRect = containerRef.current.getBoundingClientRect();
    const width = containerRect.width || window.innerWidth;
    const height = containerRect.height || window.innerHeight;

    // 场景设置
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      premultipliedAlpha: false
    });

    // 配置渲染器
    renderer.setSize(width, height);
    renderer.setClearColor(0x000000, 0); // 完全透明背景
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能

    // 设置canvas样式
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.pointerEvents = 'none';
    renderer.domElement.style.zIndex = '1';

    containerRef.current.appendChild(renderer.domElement);
    console.log('渲染器初始化完成, 尺寸:', width, 'x', height);

    // 保存引用
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // 地球半径
    const EARTH_RADIUS = 2;

    // 创建地球组
    const earthGroup = new THREE.Group();
    scene.add(earthGroup);

    // 创建粒子系统组
    const particleGroup = new THREE.Group();
    scene.add(particleGroup);

    // 获取CSS变量颜色
    const getThemeColor = (variable: string) => {
      try {
        const color = getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
        console.log(`获取颜色 ${variable}:`, color);

        // 如果颜色为空或无效，使用默认颜色
        if (!color || color === '') {
          console.warn(`颜色变量 ${variable} 为空，使用默认颜色`);
          return variable === '--color-primary' ? '#007aff' : '#5856d6';
        }

        return color;
      } catch (error) {
        console.error(`获取颜色变量 ${variable} 失败:`, error);
        return variable === '--color-primary' ? '#007aff' : '#5856d6';
      }
    };

    // 监听主题变化
    const updateColors = () => {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');

      // 更新线条材质颜色
      earthGroup.children.forEach(child => {
        if (child instanceof THREE.Line) {
          (child.material as THREE.LineBasicMaterial).color.set(primaryColor);
        }
      });

      // 更新粒子颜色以响应主题切换
      updateParticleColors();
    };

    // 将经纬度转换为3D坐标
    function latLngToVector3(lat: number, lng: number, radius = EARTH_RADIUS) {
      const phi = (90 - lat) * (Math.PI / 180);
      const theta = (lng + 180) * (Math.PI / 180);

      const x = -(radius * Math.sin(phi) * Math.cos(theta));
      const z = (radius * Math.sin(phi) * Math.sin(theta));
      const y = (radius * Math.cos(phi));

      return new THREE.Vector3(x, y, z);
    }

    // 从坐标数组创建线条几何体
    function createLineFromCoordinates(coordinates: number[][]) {
      const points: THREE.Vector3[] = [];
      coordinates.forEach(coord => {
        if (coord.length >= 2) {
          const [lng, lat] = coord;
          points.push(latLngToVector3(lat, lng));
        }
      });
      return new THREE.BufferGeometry().setFromPoints(points);
    }

    // 处理GeoJSON数据并创建线框
    function processGeoJSON(geoData: any) {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');
      
      const lineMaterial = new THREE.LineBasicMaterial({
        color: new THREE.Color(primaryColor),
        transparent: true,
        opacity: 0.8,
      });

      geoData.features.forEach((feature: any) => {
        if (feature.geometry) {
          const { type, coordinates } = feature.geometry;

          if (type === 'Polygon') {
            coordinates.forEach((ring: number[][]) => {
              const geometry = createLineFromCoordinates(ring);
              const line = new THREE.Line(geometry, lineMaterial);
              earthGroup.add(line);
            });
          } else if (type === 'MultiPolygon') {
            coordinates.forEach((polygon: number[][][]) => {
              polygon.forEach((ring: number[][]) => {
                const geometry = createLineFromCoordinates(ring);
                const line = new THREE.Line(geometry, lineMaterial);
                earthGroup.add(line);
              });
            });
          }
        }
      });
    }



    // 加载世界地理数据
    async function loadWorldData() {
      console.log('正在加载世界地理数据...');
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoData = await response.json();
      console.log('地理数据加载成功，包含', geoData.features.length, '个国家/地区');

      // 添加真实的地理数据
      processGeoJSON(geoData);
      console.log('真实地球线框渲染完成');
    }

    // 备选方案：创建简单的球体线框
    function createFallbackSphere() {
      console.log('使用备选球体线框');
      const sphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 16);
      const sphereMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color(getThemeColor('--color-primary')),
        wireframe: true,
        transparent: true,
        opacity: 0.8
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      earthGroup.add(sphere);
      console.log('备选球体添加完成');
    }

    // 获取主题响应式粒子颜色的函数
    function getParticleColors() {
      const isDarkTheme = document.documentElement.classList.contains('dark');

      if (isDarkTheme) {
        // 深色主题：使用较亮的颜色确保在深色背景下可见
        return {
          spatial: new THREE.Color('#4FC3F7'),    // 亮蓝色 - 空间分析
          urban: new THREE.Color('#BA68C8'),      // 亮紫色 - 城市计算
          economic: new THREE.Color('#81C784')    // 亮绿色 - 经济建模
        };
      } else {
        // 浅色主题：使用较深的颜色确保在浅色背景下可见
        return {
          spatial: new THREE.Color('#1565C0'),    // 深蓝色 - 空间分析
          urban: new THREE.Color('#7B1FA2'),      // 深紫色 - 城市计算
          economic: new THREE.Color('#388E3C')    // 深绿色 - 经济建模
        };
      }
    }

    // 更新粒子颜色以响应主题切换
    function updateParticleColors() {
      if (!particleSystemRef.current) return;

      const particles = particleSystemRef.current as any;
      const particleData = particles.particleData;
      const geometry = particles.particleGeometry;

      if (!particleData || !geometry) return;

      const colors = geometry.attributes.color.array;
      const particleColors = getParticleColors();

      console.log('更新粒子颜色，当前主题:', document.documentElement.classList.contains('dark') ? 'dark' : 'light');

      // 更新每个粒子的颜色
      for (let i = 0; i < particleData.length; i++) {
        const data = particleData[i];
        let color: THREE.Color;

        if (data.colorType === 'spatial') {
          color = particleColors.spatial;
        } else if (data.colorType === 'urban') {
          color = particleColors.urban;
        } else {
          color = particleColors.economic;
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;
      }

      // 标记颜色属性需要更新
      geometry.attributes.color.needsUpdate = true;
    }

    // 创建精致的数据点粒子系统 - 模拟地理计算中的关键数据节点
    function createDataParticles() {
      const particleCount = 10; // 进一步精简数量，确保精致效果

      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);

      // 获取主题响应式颜色
      const particleColors = getParticleColors();

      // 为每个粒子创建独立的运动参数
      const particleData: Array<{
        baseRadius: number;
        orbitSpeed: number;
        heightSpeed: number;
        heightAmplitude: number;
        phaseOffset: number;
        orbitOffset: number;
        basePhi: number;
        baseTheta: number;
        colorType: 'spatial' | 'urban' | 'economic'; // 添加颜色类型，用于主题切换
      }> = [];

      // 在地球表面精心分布少量数据点，模拟重要的地理节点
      for (let i = 0; i < particleCount; i++) {
        // 使用改进的球面坐标系统，避免极点聚集
        const phi = Math.random() * Math.PI * 2; // 经度 (0 到 2π)
        const u = Math.random(); // 均匀分布的随机数
        const theta = Math.acos(2 * u - 1); // 改进的纬度分布，避免极点过密

        // 确定粒子的颜色类型
        const colorChoice = Math.random();
        let colorType: 'spatial' | 'urban' | 'economic';
        if (colorChoice < 0.4) {
          colorType = 'spatial'; // 空间分析数据点
        } else if (colorChoice < 0.7) {
          colorType = 'urban'; // 城市计算数据点
        } else {
          colorType = 'economic'; // 经济建模数据点
        }

        // 为每个粒子创建独立的运动参数
        const particleInfo = {
          baseRadius: EARTH_RADIUS + 0.1 + Math.random() * 0.4, // 距离地球0.1-0.5单位的随机高度
          orbitSpeed: 0.0003 + Math.random() * 0.0007, // 更缓慢的轨道速度，确保自然流畅
          heightSpeed: 0.0005 + Math.random() * 0.0008, // 更柔和的高度变化速度
          heightAmplitude: 0.03 + Math.random() * 0.08, // 更小的高度变化幅度，避免过于突兀
          phaseOffset: Math.random() * Math.PI * 2, // 运动相位偏移
          orbitOffset: Math.random() * Math.PI * 2, // 轨道偏移
          basePhi: phi,
          baseTheta: theta,
          colorType: colorType // 保存颜色类型用于主题切换
        };
        particleData.push(particleInfo);

        // 初始位置计算
        const radius = particleInfo.baseRadius;
        const x = radius * Math.sin(theta) * Math.cos(phi);
        const y = radius * Math.cos(theta);
        const z = radius * Math.sin(theta) * Math.sin(phi);

        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;

        // 根据已确定的颜色类型分配颜色
        let color: THREE.Color;
        if (particleInfo.colorType === 'spatial') {
          color = particleColors.spatial; // 空间分析数据点
        } else if (particleInfo.colorType === 'urban') {
          color = particleColors.urban; // 城市计算数据点
        } else {
          color = particleColors.economic; // 经济建模数据点
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;
      }

      const geometry = new THREE.BufferGeometry();
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

      // 创建清晰的纯色圆形纹理，去除光晕效果
      const canvas = document.createElement('canvas');
      canvas.width = 32; // 减小纹理尺寸，适配更小的粒子
      canvas.height = 32;
      const context = canvas.getContext('2d')!;

      // 清除画布
      context.clearRect(0, 0, 32, 32);

      // 绘制纯色圆形，无渐变无光晕
      context.fillStyle = 'rgba(255, 255, 255, 1)'; // 纯白色，完全不透明
      context.beginPath();
      context.arc(16, 16, 14, 0, Math.PI * 2); // 绘制清晰的圆形，留1像素边距
      context.fill();

      const texture = new THREE.CanvasTexture(canvas);
      texture.generateMipmaps = false; // 禁用mipmap保持锐利边缘
      texture.minFilter = THREE.NearestFilter; // 使用最近邻过滤保持清晰度
      texture.magFilter = THREE.NearestFilter;
      texture.magFilter = THREE.LinearFilter;

      // 创建极小的纯色圆形粒子材质
      const material = new THREE.PointsMaterial({
        size: 0.8, // 极小尺寸，确保在0.8-1.2像素范围内（根据距离变化）
        map: texture, // 使用清晰的纯色圆形纹理
        vertexColors: true,
        transparent: true, // 需要透明度来处理圆形边缘
        opacity: 1.0, // 完全不透明
        blending: THREE.NormalBlending, // 使用正常混合模式，避免光晕效果
        depthWrite: false, // 禁用深度写入，避免透明度问题
        sizeAttenuation: true, // 启用距离衰减，远处粒子更小
        alphaTest: 0.8 // 高阈值，确保只显示圆形中心部分，边缘清晰
      });

      const particles = new THREE.Points(geometry, material);

      // 保存粒子数据和几何体引用，用于动画更新
      (particles as any).particleData = particleData;
      (particles as any).particleGeometry = geometry;

      // 将粒子添加到地球组，使其跟随地球旋转
      earthGroup.add(particles);
      particleSystemRef.current = particles;

      console.log('精致圆形数据点系统创建完成，包含', particleCount, '个动态小圆点光效');
    }







    // 设置相机位置
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // 更新粒子独立运动的函数
    function updateParticleMotion(time: number) {
      if (!particleSystemRef.current) return;

      const particles = particleSystemRef.current as any;
      const particleData = particles.particleData;
      const geometry = particles.particleGeometry;

      if (!particleData || !geometry) return;

      const positions = geometry.attributes.position.array;

      // 更新每个粒子的位置
      for (let i = 0; i < particleData.length; i++) {
        const data = particleData[i];

        // 计算当前时间的轨道位置
        const orbitTime = time * data.orbitSpeed + data.orbitOffset;
        const heightTime = time * data.heightSpeed + data.phaseOffset;

        // 计算动态半径（柔和的高度变化）
        const heightVariation = Math.sin(heightTime) * data.heightAmplitude;
        const secondaryHeight = Math.cos(heightTime * 1.3) * data.heightAmplitude * 0.3; // 添加次级高度变化
        const dynamicRadius = data.baseRadius + heightVariation + secondaryHeight;

        // 计算动态角度（更自然的轨道运动）
        const primaryOrbit = Math.sin(orbitTime) * 0.12; // 主要轨道摆动
        const secondaryOrbit = Math.sin(orbitTime * 1.7) * 0.05; // 次级轨道摆动
        const dynamicPhi = data.basePhi + primaryOrbit + secondaryOrbit;

        const primaryTilt = Math.cos(orbitTime * 0.8) * 0.08; // 主要倾斜
        const secondaryTilt = Math.cos(orbitTime * 2.1) * 0.03; // 次级倾斜
        const dynamicTheta = data.baseTheta + primaryTilt + secondaryTilt;

        // 球面坐标转换为笛卡尔坐标
        const x = dynamicRadius * Math.sin(dynamicTheta) * Math.cos(dynamicPhi);
        const y = dynamicRadius * Math.cos(dynamicTheta);
        const z = dynamicRadius * Math.sin(dynamicTheta) * Math.sin(dynamicPhi);

        // 更新位置
        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;
      }

      // 标记几何体需要更新
      geometry.attributes.position.needsUpdate = true;
    }

    // 主动画循环 - 控制所有视觉效果的更新
    function animate() {
      animationIdRef.current = requestAnimationFrame(animate);
      const time = Date.now() * 0.001; // 获取当前时间，用于所有动画计算

      // 缓慢旋转地球，模拟地球自转
      earthGroup.rotation.y += 0.0015; // 进一步优化旋转速度
      earthGroup.rotation.x += 0.0003; // 轻微的倾斜旋转

      // 更新粒子的独立运动
      updateParticleMotion(time);

      // 渲染场景
      renderer.render(scene, camera);
    }

    // 添加调试信息
    console.log('场景对象数量:', scene.children.length);
    console.log('地球组对象数量:', earthGroup.children.length);

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.width || window.innerWidth;
      const newHeight = containerRect.height || window.innerHeight;

      camera.aspect = newWidth / newHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(newWidth, newHeight);
      console.log('窗口大小调整:', newWidth, 'x', newHeight);
    };

    window.addEventListener('resize', handleResize);

    // 监听主题变化
    let observer: MutationObserver | null = null;

    const initThemeObserver = () => {
      observer = new MutationObserver(() => {
        updateColors();
      });
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    };

    // 开始动画
    animate();

    // 初始化主题监听器
    initThemeObserver();

    // 尝试加载真实地理数据，失败时使用备选球体
    loadWorldData().then(() => {
      console.log('真实地理数据加载并渲染完成');
      console.log('当前地球组包含对象数量:', earthGroup.children.length);

      // 创建粒子系统效果
      createDataParticles();
      console.log('精致粒子系统效果创建完成');
    }).catch((error) => {
      console.log('地理数据加载失败，使用备选球体:', error.message);
      createFallbackSphere();
      console.log('备选球体创建完成，地球组包含对象数量:', earthGroup.children.length);

      // 即使使用备选球体，也创建粒子系统效果
      createDataParticles();
      console.log('备选模式下精致粒子系统效果创建完成');
    });

    // 组件清理函数 - 释放所有THREE.js资源和事件监听器
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      // 清理粒子系统资源
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.PointsMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = particleSystemRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        particleSystemRef.current = null;
      }

      // 清理渲染器和DOM元素
      if (rendererRef.current && containerRef.current && containerRef.current.contains(rendererRef.current.domElement)) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
        rendererRef.current = null;
      }

      // 清理场景
      if (sceneRef.current) {
        sceneRef.current.clear();
        sceneRef.current = null;
      }



      console.log('CountryOutlineBackground: 所有资源清理完成');
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2, // 提高z-index，但仍低于文字内容(z-index: 10)
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default CountryOutlineBackground;
